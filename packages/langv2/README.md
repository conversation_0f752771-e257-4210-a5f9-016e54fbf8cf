# @ht/hai-code-cli

基于 LangChain.js 与 LangGraph 的 AI Coding CLI 。

## CLI 选项与环境变量

CLI（`hai-code`）主要选项：

- `-m, --model <model>`：指定模型（默认：`ht::saas-deepseek-v3`）
- `-p, --prompt <prompt>`：非交互模式直接处理单条 Prompt
- `-b, --base-url <url>`：OpenAI-Compatible Base URL（也可用 `OPENAI_BASE_URL`）
- `-i, --interactive`：交互模式
- `-d, --debug`：调试日志
- `-h, --help` / `-v, --version`

常用环境变量：

- `OPENAI_API_KEY` / `OPENAI_BASE_URL`
- `ANTHROPIC_API_KEY`
- `GEMINI_API_KEY` / `GOOGLE_CLOUD_PROJECT`
